Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-06T09:22:36+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-06T09:22:36+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-06T09:22:37+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> customer-backend/internal/api/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/register     --> customer-backend/internal/api/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/oauth-user   --> customer-backend/internal/api/handlers.(*AuthHandler).CreateOAuthCustomer-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> customer-backend/internal/api/handlers.(*AuthHandler).Logout-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> customer-backend/internal/api/handlers.(*AuthHandler).RefreshToken-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/forgot-password --> customer-backend/internal/api/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/reset-password --> customer-backend/internal/api/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug --> customer-backend/internal/api/handlers.(*ShopHandler).GetBranchBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables --> customer-backend/internal/api/handlers.(*TableHandler).GetTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/available --> customer-backend/internal/api/handlers.(*TableHandler).GetAvailableTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/number/:tableNumber --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByNumber-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/:tableId/validate --> customer-backend/internal/api/handlers.(*TableHandler).ValidateTableForOrder-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/table-areas --> customer-backend/internal/api/handlers.(*TableHandler).GetTableAreas-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /tables/:tableId          --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByID-fm (6 handlers)
[GIN-debug] POST   /orders/table             --> customer-backend/internal/api/handlers.(*OrderHandler).CreateTableOrder-fm (6 handlers)
[GIN-debug] POST   /orders/create-with-payment --> customer-backend/internal/api/handlers.(*OrderHandler).CreateOrderWithPayment-fm (6 handlers)
[GIN-debug] GET    /orders/:orderId          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByID-fm (6 handlers)
[GIN-debug] GET    /orders/number/:orderNumber --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByNumber-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId    --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId/active --> customer-backend/internal/api/handlers.(*OrderHandler).GetActiveOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/customer          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByCustomer-fm (6 handlers)
[GIN-debug] PUT    /orders/:orderId/status   --> customer-backend/internal/api/handlers.(*OrderHandler).UpdateOrderStatus-fm (6 handlers)
[GIN-debug] POST   /payments/create-intent   --> customer-backend/internal/api/handlers.(*PaymentHandler).CreatePaymentIntent-fm (6 handlers)
[GIN-debug] POST   /payments/confirm         --> customer-backend/internal/api/handlers.(*PaymentHandler).ConfirmPayment-fm (6 handlers)
[GIN-debug] GET    /payments/:paymentIntentId/status --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPaymentStatus-fm (6 handlers)
[GIN-debug] POST   /payments/:paymentIntentId/cancel --> customer-backend/internal/api/handlers.(*PaymentHandler).CancelPayment-fm (6 handlers)
[GIN-debug] POST   /payments/webhook         --> customer-backend/internal/api/handlers.(*PaymentHandler).HandleWebhook-fm (6 handlers)
[GIN-debug] GET    /payments/config          --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPublishableKey-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/handlers.(*CartHandler).GetCart-fm (6 handlers)
[GIN-debug] POST   /cart/add                 --> customer-backend/internal/api/handlers.(*CartHandler).AddToCart-fm (6 handlers)
[GIN-debug] PUT    /cart/update              --> customer-backend/internal/api/handlers.(*CartHandler).UpdateQuantity-fm (6 handlers)
[GIN-debug] DELETE /cart/remove              --> customer-backend/internal/api/handlers.(*CartHandler).RemoveFromCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear               --> customer-backend/internal/api/handlers.(*CartHandler).ClearCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear-branch        --> customer-backend/internal/api/handlers.(*CartHandler).ClearBranchCart-fm (6 handlers)
[GIN-debug] POST   /cart/sync                --> customer-backend/internal/api/handlers.(*CartHandler).SyncCartOnLogin-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-06T09:22:37+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T09:22:49+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T09:22:49+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T09:22:49+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T09:22:49+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T09:22:49+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 09:22:49 +07] \"GET /cart HTTP/1.1 200 188.098167ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T09:22:49+07:00"}

2025/06/06 09:22:49 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:350 [33mSLOW SQL >= 200ms
[0m[31;1m[305.983ms] [33m[rows:2][35m SELECT DISTINCT "cuisine_type" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 09:22:49 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:138 [33mSLOW SQL >= 200ms
[0m[31;1m[290.590ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 09:22:50 +07] \"GET /shops/filter-options HTTP/1.1 200 411.917958ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T09:22:50+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T09:22:50+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T09:22:50+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 09:22:50 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 465.918ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T09:22:50+07:00"}

2025/06/06 09:22:50 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[479.543ms] [33m[rows:1][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749100308081_x4tt0ris2' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 09:22:50 +07] \"GET /orders/customer?customerPhone=guest_1749100308081_x4tt0ris2\u0026limit=50 HTTP/1.1 200 480.168625ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T09:22:50+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: the-green-terrace, page=1, limit=20","time":"2025-06-06T09:22:54+07:00"}
{"level":"info","msg":"Looking for branch with shop slug: weerawat-poseeya, branch slug: the-green-terrace","time":"2025-06-06T09:22:54+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 09:22:56 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/the-green-terrace/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 1.387194917s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T09:22:56+07:00"}

2025/06/06 09:23:05 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:99 [35;1mrecord not found
[0m[33m[70.768ms] [34;1m[rows:0][0m SELECT * FROM "cart_items" WHERE (cart_session_id = 'fc20d5d2-cd6c-4645-a346-63e6099661d5' AND menu_item_id = '5deb0542-7b3c-4f4d-806f-58f3a5f5652a' AND shop_slug = 'weerawat-poseeya' AND branch_slug = 'the-green-terrace') AND "cart_items"."deleted_at" IS NULL ORDER BY "cart_items"."id" LIMIT 1
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 09:23:05 +07] \"POST /cart/add HTTP/1.1 200 636.435458ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T09:23:05+07:00"}
